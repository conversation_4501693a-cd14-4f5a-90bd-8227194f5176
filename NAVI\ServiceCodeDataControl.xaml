<UserControl x:Class="NAVI.ServiceCodeDataControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:NAVI.Controls"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="4"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 成功按钮样式 -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 危险按钮样式 -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFF44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFD32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- DataGrid样式 -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#FFF0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FFF8F9FA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="RowHeight" Value="50"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="ColumnWidth" Value="*"/>
        </Style>

        <!-- DataGrid列标题样式 -->
        <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#FF37474F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#FF546E7A"/>
            <Setter Property="Height" Value="50"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部内容区 -->
        <Border Grid.Row="0"
               Background="White"
               BorderBrush="#FFE0E0E0"
               BorderThickness="0,0,0,1"
               Padding="20,16">
            <StackPanel>
                <!-- 标题区域 -->
                <Border Background="#FFF8F9FA"
                       BorderBrush="#FFE0E0E0"
                       BorderThickness="1"
                       CornerRadius="6"
                       Padding="16,12"
                       Margin="0,0,0,16">
                    <StackPanel>
                        <!-- 主标题 -->
                        <TextBlock Text="福祉短期入所サービス料金表"
                                  FontSize="20"
                                  FontWeight="Medium"
                                  Foreground="#FF2986A8"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,12"/>

                        <!-- 数据概要 - 紧凑布局 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="総レコード数:" FontSize="12" Foreground="#FF757575" VerticalAlignment="Center"/>
                            <TextBlock x:Name="TotalRecordsText" Text="0件" FontSize="12" FontWeight="Medium" Foreground="#FF2986A8" VerticalAlignment="Center" Margin="4,0,16,0"/>

                            <TextBlock Text="表示レコード数:" FontSize="12" Foreground="#FF757575" VerticalAlignment="Center"/>
                            <TextBlock x:Name="DisplayRecordsText" Text="0件" FontSize="12" FontWeight="Medium" Foreground="#FF2986A8" VerticalAlignment="Center" Margin="4,0,16,0"/>

                            <TextBlock Text="サービス種類:" FontSize="12" Foreground="#FF757575" VerticalAlignment="Center"/>
                            <TextBlock Text="福祉短期入所サービス（障害支援区分1～6）" FontSize="12" FontWeight="Medium" Foreground="#FF2986A8" VerticalAlignment="Center" Margin="4,0,16,0"/>

                            <TextBlock Text="料金体系:" FontSize="12" Foreground="#FF757575" VerticalAlignment="Center"/>
                            <TextBlock Text="国事単価、都道府、都道府単価を含む" FontSize="12" FontWeight="Medium" Foreground="#FF2986A8" VerticalAlignment="Center" Margin="4,0,0,0"/>
                        </StackPanel>

                        <!-- 状态信息 -->
                        <TextBlock x:Name="DataStatusText"
                                  Text="データ状態: CSVファイルを読み込んでください"
                                  FontSize="12"
                                  Foreground="#FFF44336"
                                  HorizontalAlignment="Center"
                                  Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>

                <!-- 操作区域 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="10"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 主要操作按钮 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <!--<Button x:Name="ImportCsvButton"
                               Content="📁 CSVファイルを読み込み"
                               Style="{StaticResource SuccessButtonStyle}"
                               Tag="{Binding}"
                               Width="180" Height="32"
                               Margin="0,0,12,0"
                               Click="ImportCsvButton_Click"/>-->

                        <Button x:Name="ExportCsvButton"
                               Content="📤 CSVエクスポート"
                               Style="{StaticResource ModernButtonStyle}"
                               Tag="{Binding}"
                               Width="140" Height="32"
                               Click="ExportCsvButton_Click"/>
                    </StackPanel>

                    <!-- 搜索过滤区域 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <TextBlock Text="サービスコード:"
                                  VerticalAlignment="Center"
                                  Margin="0,0,6,0"
                                  FontSize="12"/>
                        <TextBox x:Name="ServiceCodeTextBox"
                                Width="140" Height="32"
                                Style="{StaticResource ModernTextBoxStyle}"
                                Margin="0,0,16,0"
                                Text="1xxx"/>

                        <TextBlock Text="障害支援区分:"
                                  VerticalAlignment="Center"
                                  Margin="0,0,6,0"
                                  FontSize="12"/>
                        <ComboBox x:Name="DisabilitySupportComboBox"
                                 Width="100" Height="32"
                                 Margin="0,0,16,0"
                                 SelectedIndex="0">
                            <ComboBoxItem Content="全て"/>
                            <ComboBoxItem Content="1"/>
                            <ComboBoxItem Content="2"/>
                            <ComboBoxItem Content="3"/>
                            <ComboBoxItem Content="4"/>
                            <ComboBoxItem Content="5"/>
                            <ComboBoxItem Content="6"/>
                        </ComboBox>

                        <TextBlock Text="人員配置区分:"
                                  VerticalAlignment="Center"
                                  Margin="0,0,6,0"
                                  FontSize="12"/>
                        <ComboBox x:Name="StaffingComboBox"
                                 Width="100" Height="32"
                                 SelectedIndex="0">
                            <ComboBoxItem Content="全て"/>
                        </ComboBox>

                        <TextBlock Text="サービス名で検索:"
                                  VerticalAlignment="Center"
                                  Margin="16,0,6,0"
                                  FontSize="12"/>
                        <TextBox x:Name="ServiceNameTextBox"
                                Width="140" Height="32"
                                Style="{StaticResource ModernTextBoxStyle}"
                                Text=""/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 数据表格区域 -->
        <Grid Grid.Row="1" Margin="20,8,20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 表格提示区域 -->
            <Border Grid.Row="0"
                   Background="#FFF8F9FA"
                   BorderBrush="#FFE0E0E0"
                   BorderThickness="1,1,1,0"
                   CornerRadius="6,6,0,0"
                   Padding="16,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="📁" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="CSVファイルを読み込んでデータを表示してください"
                                  FontSize="13"
                                  Foreground="#FF757575"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <TextBlock Grid.Column="2"
                              Text="上の「📁 CSVファイル読み込み」ボタンから「服務代.csv」ファイルを選択してください"
                              FontSize="12"
                              Foreground="#FF9E9E9E"
                              VerticalAlignment="Center"/>
                </Grid>
            </Border>

            <!-- 数据表格 -->
            <Border Grid.Row="1"
                   Background="White"
                   BorderBrush="#FFE0E0E0"
                   BorderThickness="1,0,1,1"
                   CornerRadius="0,0,6,6">
                <DataGrid x:Name="ServiceCodeDataGrid"
                         Style="{StaticResource ModernDataGridStyle}"
                         ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                         Margin="0">
                    <DataGrid.Columns>
                        <!-- 其他列将动态生成 -->
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>

        <!-- 分页控件 -->
        <controls:PaginationControl x:Name="PaginationControl"
                                   Grid.Row="2"
                                   Margin="20,8,20,20"
                                   PageChanged="PaginationControl_PageChanged"
                                   PageSizeChanged="PaginationControl_PageSizeChanged"/>
    </Grid>
</UserControl>
