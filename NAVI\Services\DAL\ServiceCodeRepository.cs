using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 服务代码主数据实体类
    /// </summary>
    public class ServiceCodeMaster
    {
        public string サービスコード { get; set; } = string.Empty;
        public string サービス内容略称 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string 合成単位 { get; set; } = string.Empty;
        public string 級地コード { get; set; } = string.Empty;
        public string 単位数単価 { get; set; } = string.Empty;
        public string 国費単価 { get; set; } = string.Empty;
        public string 旧身体療護 { get; set; } = string.Empty;
        public string 都単価 { get; set; } = string.Empty;
        public string キーコード { get; set; } = string.Empty;
        public string 都加算単価 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 服务代码主数据访问类
    /// </summary>
    public class ServiceCodeRepository : BaseRepository<ServiceCodeMaster>
    {
        public ServiceCodeRepository(DatabaseService databaseService) 
            : base(databaseService, "ServiceCodeMaster")
        {
        }

        /// <summary>
        /// 根据服务代码获取服务代码主数据
        /// </summary>
        public async Task<ServiceCodeMaster> GetByServiceCodeAsync(string serviceCode)
        {
            try
            {
                var codes = await GetByConditionAsync("\"サービスコード\" = @serviceCode",
                    CreateParameter("@serviceCode", serviceCode));
                return codes.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"根据服务代码获取服务代码主数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据服务内容略称搜索服务代码主数据
        /// </summary>
        public async Task<List<ServiceCodeMaster>> SearchByServiceNameAsync(string serviceName)
        {
            try
            {
                return await GetByConditionAsync("\"サービス内容略称\" LIKE @serviceName",
                    CreateParameter("@serviceName", $"%{serviceName}%"));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据服务内容略称搜索服务代码主数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据障害支援区分获取服务代码主数据
        /// </summary>
        public async Task<List<ServiceCodeMaster>> GetByDisabilitySupportLevelAsync(string supportLevel)
        {
            try
            {
                return await GetByConditionAsync("\"障害支援区分\" = @supportLevel",
                    CreateParameter("@supportLevel", supportLevel));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据障害支援区分获取服务代码主数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据级地代码获取服务代码主数据
        /// </summary>
        public async Task<List<ServiceCodeMaster>> GetByGradeCodeAsync(string gradeCode)
        {
            try
            {
                return await GetByConditionAsync("\"級地コード\" = @gradeCode",
                    CreateParameter("@gradeCode", gradeCode));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据级地代码获取服务代码主数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查服务代码是否存在
        /// </summary>
        public async Task<bool> ServiceCodeExistsAsync(string serviceCode)
        {
            try
            {
                return await ExistsAsync("\"サービスコード\" = @serviceCode",
                    CreateParameter("@serviceCode", serviceCode));
            }
            catch (Exception ex)
            {
                throw new Exception($"检查服务代码存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建新的服务代码主数据
        /// </summary>
        public async Task<int> CreateServiceCodeAsync(ServiceCodeMaster serviceCode)
        {
            try
            {
                return await InsertAsync(serviceCode);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建服务代码主数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新服务代码主数据
        /// </summary>
        public async Task<int> UpdateServiceCodeAsync(ServiceCodeMaster serviceCode)
        {
            try
            {
                return await UpdateAsync(serviceCode, "\"サービスコード\" = @serviceCode", 
                    CreateParameter("@serviceCode", serviceCode.サービスコード));
            }
            catch (Exception ex)
            {
                throw new Exception($"更新服务代码主数据失败: {ex.Message}", ex);
            }
        }

        protected override List<ServiceCodeMaster> ConvertDataTableToList(DataTable dataTable)
        {
            var serviceCodes = new List<ServiceCodeMaster>();
            foreach (DataRow row in dataTable.Rows)
            {
                serviceCodes.Add(new ServiceCodeMaster
                {
                    サービスコード = GetStringValue(row, "サービスコード"),
                    サービス内容略称 = GetStringValue(row, "サービス内容略称"),
                    障害支援区分 = GetStringValue(row, "障害支援区分"),
                    合成単位 = GetStringValue(row, "合成単位"),
                    級地コード = GetStringValue(row, "級地コード"),
                    単位数単価 = GetStringValue(row, "単位数単価"),
                    国費単価 = GetStringValue(row, "国費単価"),
                    旧身体療護 = GetStringValue(row, "旧身体療護"),
                    都単価 = GetStringValue(row, "都単価"),
                    キーコード = GetStringValue(row, "キーコード"),
                    都加算単価 = GetStringValue(row, "都加算単価")
                });
            }
            return serviceCodes;
        }

        protected override DataTable ConvertListToDataTable(List<ServiceCodeMaster> entities)
        {
            var dataTable = new DataTable();
            var columns = GetColumnNames();
            
            foreach (var column in columns)
            {
                dataTable.Columns.Add(column);
            }

            foreach (var serviceCode in entities)
            {
                var row = dataTable.NewRow();
                row["サービスコード"] = serviceCode.サービスコード;
                row["サービス内容略称"] = serviceCode.サービス内容略称;
                row["障害支援区分"] = serviceCode.障害支援区分;
                row["合成単位"] = serviceCode.合成単位;
                row["級地コード"] = serviceCode.級地コード;
                row["単位数単価"] = serviceCode.単位数単価;
                row["国費単価"] = serviceCode.国費単価;
                row["旧身体療護"] = serviceCode.旧身体療護;
                row["都単価"] = serviceCode.都単価;
                row["キーコード"] = serviceCode.キーコード;
                row["都加算単価"] = serviceCode.都加算単価;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildInsertCommand(ServiceCodeMaster entity)
        {
            var sql = @"INSERT INTO ServiceCodeMaster 
                (""サービスコード"", ""サービス内容略称"", ""障害支援区分"", ""合成単位"", ""級地コード"", ""単位数単価"", ""国費単価"", ""旧身体療護"", ""都単価"", ""キーコード"", ""都加算単価"") 
                VALUES (@サービスコード, @サービス内容略称, @障害支援区分, @合成単位, @級地コード, @単位数単価, @国費単価, @旧身体療護, @都単価, @キーコード, @都加算単価)";

            var parameters = new[]
            {
                CreateParameter("@サービスコード", entity.サービスコード),
                CreateParameter("@サービス内容略称", entity.サービス内容略称),
                CreateParameter("@障害支援区分", entity.障害支援区分),
                CreateParameter("@合成単位", entity.合成単位),
                CreateParameter("@級地コード", entity.級地コード),
                CreateParameter("@単位数単価", entity.単位数単価),
                CreateParameter("@国費単価", entity.国費単価),
                CreateParameter("@旧身体療護", entity.旧身体療護),
                CreateParameter("@都単価", entity.都単価),
                CreateParameter("@キーコード", entity.キーコード),
                CreateParameter("@都加算単価", entity.都加算単価)
            };

            return (sql, parameters);
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(ServiceCodeMaster entity, string whereClause, SQLiteParameter[] whereParameters)
        {
            var setParts = new List<string>();
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(entity.サービス内容略称))
            {
                setParts.Add("\"サービス内容略称\" = @サービス内容略称");
                parameters.Add(CreateParameter("@サービス内容略称", entity.サービス内容略称));
            }
            if (!string.IsNullOrEmpty(entity.障害支援区分))
            {
                setParts.Add("\"障害支援区分\" = @障害支援区分");
                parameters.Add(CreateParameter("@障害支援区分", entity.障害支援区分));
            }
            if (!string.IsNullOrEmpty(entity.合成単位))
            {
                setParts.Add("\"合成単位\" = @合成単位");
                parameters.Add(CreateParameter("@合成単位", entity.合成単位));
            }
            if (!string.IsNullOrEmpty(entity.級地コード))
            {
                setParts.Add("\"級地コード\" = @級地コード");
                parameters.Add(CreateParameter("@級地コード", entity.級地コード));
            }
            if (!string.IsNullOrEmpty(entity.単位数単価))
            {
                setParts.Add("\"単位数単価\" = @単位数単価");
                parameters.Add(CreateParameter("@単位数単価", entity.単位数単価));
            }
            if (!string.IsNullOrEmpty(entity.国費単価))
            {
                setParts.Add("\"国費単価\" = @国費単価");
                parameters.Add(CreateParameter("@国費単価", entity.国費単価));
            }
            if (!string.IsNullOrEmpty(entity.旧身体療護))
            {
                setParts.Add("\"旧身体療護\" = @旧身体療護");
                parameters.Add(CreateParameter("@旧身体療護", entity.旧身体療護));
            }
            if (!string.IsNullOrEmpty(entity.都単価))
            {
                setParts.Add("\"都単価\" = @都単価");
                parameters.Add(CreateParameter("@都単価", entity.都単価));
            }
            if (!string.IsNullOrEmpty(entity.キーコード))
            {
                setParts.Add("\"キーコード\" = @キーコード");
                parameters.Add(CreateParameter("@キーコード", entity.キーコード));
            }
            if (!string.IsNullOrEmpty(entity.都加算単価))
            {
                setParts.Add("\"都加算単価\" = @都加算単価");
                parameters.Add(CreateParameter("@都加算単価", entity.都加算単価));
            }

            if (whereParameters != null)
            {
                parameters.AddRange(whereParameters);
            }

            var sql = $"UPDATE ServiceCodeMaster SET {string.Join(", ", setParts)} WHERE {whereClause}";
            return (sql, parameters.ToArray());
        }

        protected override List<string> GetColumnNames()
        {
            return new List<string>
            {
                "サービスコード", "サービス内容略称", "障害支援区分", "合成単位", "級地コード", "単位数単価", "国費単価", "旧身体療護", "都単価", "キーコード", "都加算単価"
            };
        }
    }
}
